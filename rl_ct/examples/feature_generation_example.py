#!/usr/bin/env python3
"""
Example script demonstrating how to use the FeatureGenerator.

This script shows various ways to use the feature generation functionality
to convert existing data into DiskDataLoader format.
"""

import sys
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from rl_ct.utils.feature_generator import FeatureGenerator
from rl_ct.utils.data_loaders.disk_loader import DiskDataLoader
from rl_ct.utils.logger import get_logger

logger = get_logger(__name__)


def example_basic_usage():
    """Example 1: Basic usage with default settings."""
    
    print("=== Example 1: Basic Usage ===")
    
    # Create feature generator with default settings
    generator = FeatureGenerator()
    
    # Get available symbols
    symbols = generator.get_available_symbols()
    print(f"Available symbols: {len(symbols)}")
    
    # Process a single symbol
    if symbols:
        test_symbol = symbols[0]
        print(f"Processing symbol: {test_symbol}")
        
        success = generator.process_symbol(test_symbol)
        print(f"Processing result: {success}")
        
        if success:
            # Test loading with DiskDataLoader
            loader = DiskDataLoader(
                data_dir='data/processed',
                dataset_name=test_symbol,
                file_format='parquet'
            )
            
            price_data, feature_data = loader.load_data()
            print(f"Loaded data - Price: {price_data.shape}, Features: {feature_data.shape}")


def example_custom_config():
    """Example 2: Using custom configuration."""
    
    print("\n=== Example 2: Custom Configuration ===")
    
    # Custom configuration
    config = {
        'dl_model_paths': [
            'data_snap/pred_model_5min_gain_v5_432882_online',
            'data_snap/pred_res_online_feat_kline_1m_with_vola_to_20210301_36symbols_v3'
        ],
        'price_volume_path': 'data_snap/portfolio_v1',
        'output_path': 'data/custom_processed',
        'output_format': 'parquet',
        'selected_symbols': ['BTC#USDT:USDT', 'ETH#USDT:USDT'],
        'selected_features': [
            'close', 'open', 'high', 'low', 'vol',
            'rsi_7', 'rsi_9', 'rsi_25',
            'sma_7', 'sma_9', 'sma_25',
            'macd_12', 'macd_26', 'macd_9'
        ]
    }
    
    generator = FeatureGenerator(config)
    
    # Show processing summary
    summary = generator.get_processing_summary()
    print(f"Will process {len(summary['selected_symbols'])} symbols")
    print(f"Selected features: {len(summary['selected_features'])}")
    
    # Process selected symbols
    results = generator.process_all_symbols()
    
    successful = sum(1 for success in results.values() if success)
    print(f"Successfully processed: {successful}/{len(results)} symbols")


def example_feature_analysis():
    """Example 3: Analyze available features."""
    
    print("\n=== Example 3: Feature Analysis ===")
    
    generator = FeatureGenerator()
    symbols = generator.get_available_symbols()
    
    if symbols:
        # Analyze features for a sample symbol
        sample_symbol = symbols[0]
        features = generator.get_available_features(sample_symbol)
        
        print(f"Feature analysis for {sample_symbol}:")
        print(f"Price-volume features: {len(features['price_volume'])}")
        
        # Group features by type
        feature_groups = {
            'price': ['open', 'close', 'high', 'low', 'vol', 'val', 'vwap'],
            'moving_averages': [f for f in features['price_volume'] if any(ma in f for ma in ['sma_', 'ema_', 'wma_', 'dema_', 'tema_'])],
            'momentum': [f for f in features['price_volume'] if any(mom in f for mom in ['rsi_', 'mom_', 'roc_'])],
            'volatility': [f for f in features['price_volume'] if any(vol in f for vol in ['var_', 'stddev_', 'atr_'])],
            'trend': [f for f in features['price_volume'] if any(trend in f for trend in ['macd_', 'adx_', 'cci_'])]
        }
        
        for group_name, group_features in feature_groups.items():
            available_features = [f for f in group_features if f in features['price_volume']]
            print(f"  {group_name}: {len(available_features)} features")
            if available_features:
                print(f"    Examples: {available_features[:3]}")
        
        # Show deep learning model features
        for model_name, model_features in features['dl_models'].items():
            print(f"  {model_name}: {len(model_features)} features")
            print(f"    Features: {model_features}")


def example_batch_processing():
    """Example 4: Batch processing with progress tracking."""
    
    print("\n=== Example 4: Batch Processing ===")
    
    config = {
        'output_path': 'data/batch_processed',
        'output_format': 'parquet',
        'selected_symbols': None  # Process all symbols
    }
    
    generator = FeatureGenerator(config)
    symbols = generator.get_available_symbols()
    
    # Process in batches (for demonstration, we'll just process first 5)
    batch_size = 5
    symbols_to_process = symbols[:batch_size]
    
    print(f"Processing {len(symbols_to_process)} symbols in batch...")
    
    results = {}
    for i, symbol in enumerate(symbols_to_process, 1):
        print(f"Processing {i}/{len(symbols_to_process)}: {symbol}")
        results[symbol] = generator.process_symbol(symbol)
    
    # Summary
    successful = sum(1 for success in results.values() if success)
    print(f"Batch processing complete: {successful}/{len(results)} symbols processed")
    
    # Show failed symbols
    failed_symbols = [symbol for symbol, success in results.items() if not success]
    if failed_symbols:
        print(f"Failed symbols: {failed_symbols}")


def example_data_validation():
    """Example 5: Data validation after processing."""
    
    print("\n=== Example 5: Data Validation ===")
    
    # Process a symbol first
    generator = FeatureGenerator({'output_path': 'data/validation_test'})
    symbols = generator.get_available_symbols()
    
    if symbols:
        test_symbol = symbols[0]
        success = generator.process_symbol(test_symbol)
        
        if success:
            # Load and validate the processed data
            loader = DiskDataLoader(
                data_dir='data/validation_test',
                dataset_name=test_symbol,
                file_format='parquet'
            )
            
            price_data, feature_data = loader.load_data()
            
            print(f"Data validation for {test_symbol}:")
            print(f"  Price data shape: {price_data.shape}")
            print(f"  Feature data shape: {feature_data.shape}")
            
            # Check data quality
            import numpy as np
            
            price_nan_ratio = np.isnan(price_data).sum() / price_data.size
            feature_nan_ratio = np.isnan(feature_data).sum() / feature_data.size
            
            print(f"  Price data NaN ratio: {price_nan_ratio:.4f}")
            print(f"  Feature data NaN ratio: {feature_nan_ratio:.4f}")
            
            # Check data ranges
            if not np.isnan(price_data).all():
                print(f"  Price data range: [{np.nanmin(price_data):.6f}, {np.nanmax(price_data):.6f}]")
            
            if not np.isnan(feature_data).all():
                print(f"  Feature data range: [{np.nanmin(feature_data):.6f}, {np.nanmax(feature_data):.6f}]")
            
            # Validate data consistency
            is_valid_price = loader.validate_data(price_data)
            is_valid_feature = loader.validate_data(feature_data)
            
            print(f"  Price data validation: {'PASS' if is_valid_price else 'FAIL'}")
            print(f"  Feature data validation: {'PASS' if is_valid_feature else 'FAIL'}")


def main():
    """Run all examples."""
    
    print("Feature Generation Examples")
    print("=" * 50)
    
    try:
        # Run examples
        example_basic_usage()
        example_custom_config()
        example_feature_analysis()
        example_batch_processing()
        example_data_validation()
        
        print("\n" + "=" * 50)
        print("All examples completed successfully!")
        
    except Exception as e:
        print(f"Example failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
