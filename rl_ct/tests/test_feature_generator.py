"""
Test script for FeatureGenerator functionality.
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from rl_ct.utils.feature_generator import FeatureGenerator
from rl_ct.utils.logger import get_logger

logger = get_logger(__name__)


def test_feature_generator():
    """Test basic FeatureGenerator functionality."""
    
    print("=== Testing FeatureGenerator ===")
    
    # Create temporary output directory
    with tempfile.TemporaryDirectory() as temp_dir:
        config = {
            'dl_model_paths': [
                'data_snap/pred_model_5min_gain_v5_432882_online',
                'data_snap/pred_res_online_feat_kline_1m_with_vola_to_20210301_36symbols_v3'
            ],
            'price_volume_path': 'data_snap/portfolio_v1',
            'output_path': temp_dir,
            'output_format': 'parquet'
        }
        
        generator = FeatureGenerator(config)
        
        # Test 1: Get available symbols
        print("\n1. Testing get_available_symbols()...")
        symbols = generator.get_available_symbols()
        print(f"Found {len(symbols)} symbols")
        if symbols:
            print(f"First 5 symbols: {symbols[:5]}")
        
        # Test 2: Get processing summary
        print("\n2. Testing get_processing_summary()...")
        summary = generator.get_processing_summary()
        print(f"Total symbols: {summary['total_symbols']}")
        print(f"DL model paths: {len(summary['dl_model_paths'])}")
        
        # Test 3: Get available features for a sample symbol
        if symbols:
            sample_symbol = symbols[0]
            print(f"\n3. Testing get_available_features() for {sample_symbol}...")
            features = generator.get_available_features(sample_symbol)
            
            print(f"Price-volume features: {len(features['price_volume'])}")
            if features['price_volume']:
                print(f"Sample PV features: {features['price_volume'][:5]}")
            
            for model_name, model_features in features['dl_models'].items():
                print(f"{model_name} features: {len(model_features)}")
                print(f"Sample features: {model_features}")
        
        # Test 4: Process a single symbol (if available)
        if symbols:
            test_symbol = symbols[0]
            print(f"\n4. Testing process_symbol() for {test_symbol}...")
            
            try:
                success = generator.process_symbol(test_symbol)
                print(f"Processing result: {success}")
                
                if success:
                    # Check output files
                    output_dir = Path(temp_dir) / test_symbol
                    print(f"Output directory: {output_dir}")
                    print(f"Files created: {list(output_dir.iterdir())}")
                    
                    # Check file sizes
                    for file_path in output_dir.iterdir():
                        if file_path.is_file():
                            size = file_path.stat().st_size
                            print(f"  {file_path.name}: {size} bytes")
                
            except Exception as e:
                print(f"Error processing symbol: {e}")
        
        print("\n=== Test completed ===")


def test_command_line_tool():
    """Test the command line tool."""
    
    print("\n=== Testing Command Line Tool ===")
    
    # Test summary command
    print("\n1. Testing --summary option...")
    os.system("python rl_ct/scripts/generate_features.py --summary")
    
    # Test dry-run with specific symbols
    print("\n2. Testing --dry-run option...")
    os.system("python rl_ct/scripts/generate_features.py --symbols BTC#USDT:USDT --dry-run")
    
    print("\n=== Command line test completed ===")


def main():
    """Main test function."""
    
    # Check if data directories exist
    required_paths = [
        'data_snap/portfolio_v1',
        'data_snap/pred_model_5min_gain_v5_432882_online'
    ]
    
    missing_paths = []
    for path in required_paths:
        if not Path(path).exists():
            missing_paths.append(path)
    
    if missing_paths:
        print("Warning: The following required data paths are missing:")
        for path in missing_paths:
            print(f"  - {path}")
        print("\nSome tests may fail or be skipped.")
        print("Please ensure the data directories exist and contain the expected data files.")
    
    # Run tests
    try:
        test_feature_generator()
        test_command_line_tool()
        
        print("\n" + "="*50)
        print("All tests completed successfully!")
        print("="*50)
        
    except Exception as e:
        print(f"\nTest failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
