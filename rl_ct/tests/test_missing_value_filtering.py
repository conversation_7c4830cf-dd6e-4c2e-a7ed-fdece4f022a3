#!/usr/bin/env python3
"""
Test script for missing value filtering functionality in FeatureGenerator.
"""

import sys
import tempfile
import numpy as np
import pandas as pd
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from rl_ct.utils.feature_generator import FeatureGenerator
from rl_ct.utils.logger import get_logger

logger = get_logger(__name__)


def create_test_data_with_varying_missing():
    """Create test data with varying levels of missing values."""
    
    # Create sample data with different missing patterns
    dates = pd.date_range('2023-01-01', periods=100, freq='1min')
    
    data = pd.DataFrame({
        'datatime': dates,
        'symbol': 'TEST#USDT:USDT',
        
        # Features with different missing levels
        'no_missing': np.random.randn(100),  # 0% missing
        'low_missing': np.random.randn(100),  # 5% missing
        'moderate_missing': np.random.randn(100),  # 20% missing
        'high_missing': np.random.randn(100),  # 60% missing
        'critical_missing': np.random.randn(100),  # 90% missing
        'all_missing': np.full(100, np.nan),  # 100% missing
        
        # Some normal features
        'price_feature': np.random.randn(100) * 0.01 + 100,
        'volume_feature': np.random.randint(1000, 10000, 100),
        'technical_indicator': np.random.randn(100) * 10 + 50,
    })
    
    # Introduce missing values with specific patterns
    
    # Low missing (5%)
    low_missing_indices = np.random.choice(100, size=5, replace=False)
    data.loc[low_missing_indices, 'low_missing'] = np.nan
    
    # Moderate missing (20%)
    moderate_missing_indices = np.random.choice(100, size=20, replace=False)
    data.loc[moderate_missing_indices, 'moderate_missing'] = np.nan
    
    # High missing (60%)
    high_missing_indices = np.random.choice(100, size=60, replace=False)
    data.loc[high_missing_indices, 'high_missing'] = np.nan
    
    # Critical missing (90%)
    critical_missing_indices = np.random.choice(100, size=90, replace=False)
    data.loc[critical_missing_indices, 'critical_missing'] = np.nan
    
    return data


def test_missing_value_filtering_basic():
    """Test basic missing value filtering functionality."""
    
    print("=== Test 1: Basic Missing Value Filtering ===")
    
    # Create test data
    test_data = create_test_data_with_varying_missing()
    
    # Show original missing statistics
    print("Original data missing statistics:")
    for col in test_data.columns:
        if col not in ['datatime', 'symbol']:
            missing_count = test_data[col].isna().sum()
            missing_ratio = missing_count / len(test_data)
            print(f"  {col}: {missing_count}/100 ({missing_ratio:.1%})")
    
    # Create feature generator with default threshold (50%)
    config = {
        'filter_high_missing': True,
        'missing_threshold': 0.5,
        'missing_threshold_mode': 'ratio'
    }
    generator = FeatureGenerator(config)
    
    # Apply filtering
    filtered_data = generator.filter_high_missing_features(test_data)
    
    print(f"\nOriginal features: {len(test_data.columns) - 2}")
    print(f"Filtered features: {len(filtered_data.columns) - 2}")
    
    # Check which features were kept/dropped
    original_features = set(test_data.columns) - {'datatime', 'symbol'}
    filtered_features = set(filtered_data.columns) - {'datatime', 'symbol'}
    dropped_features = original_features - filtered_features
    
    print(f"Dropped features: {sorted(dropped_features)}")
    print(f"Kept features: {sorted(filtered_features)}")
    
    # Verify expected behavior
    expected_dropped = {'high_missing', 'critical_missing', 'all_missing'}
    expected_kept = {'no_missing', 'low_missing', 'moderate_missing', 'price_feature', 'volume_feature', 'technical_indicator'}
    
    assert dropped_features == expected_dropped, f"Expected to drop {expected_dropped}, but dropped {dropped_features}"
    assert filtered_features == expected_kept, f"Expected to keep {expected_kept}, but kept {filtered_features}"
    
    print("✓ Basic filtering test passed")


def test_different_thresholds():
    """Test filtering with different thresholds."""
    
    print("\n=== Test 2: Different Thresholds ===")
    
    test_data = create_test_data_with_varying_missing()
    
    # Test different thresholds
    thresholds = [0.1, 0.3, 0.7, 0.95]
    
    for threshold in thresholds:
        config = {
            'filter_high_missing': True,
            'missing_threshold': threshold,
            'missing_threshold_mode': 'ratio'
        }
        generator = FeatureGenerator(config)
        
        filtered_data = generator.filter_high_missing_features(test_data)
        
        original_count = len(test_data.columns) - 2
        filtered_count = len(filtered_data.columns) - 2
        dropped_count = original_count - filtered_count
        
        print(f"Threshold {threshold:.1%}: {dropped_count} features dropped, {filtered_count} kept")
    
    print("✓ Different thresholds test passed")


def test_count_mode_threshold():
    """Test filtering with count-based threshold."""
    
    print("\n=== Test 3: Count-Based Threshold ===")
    
    test_data = create_test_data_with_varying_missing()
    
    # Use count-based threshold (drop features with >30 missing values)
    config = {
        'filter_high_missing': True,
        'missing_threshold': 30,
        'missing_threshold_mode': 'count'
    }
    generator = FeatureGenerator(config)
    
    filtered_data = generator.filter_high_missing_features(test_data)
    
    original_features = set(test_data.columns) - {'datatime', 'symbol'}
    filtered_features = set(filtered_data.columns) - {'datatime', 'symbol'}
    dropped_features = original_features - filtered_features
    
    print(f"Count threshold 30: Dropped {sorted(dropped_features)}")
    
    # Should drop features with >30 missing values (high_missing=60, critical_missing=90, all_missing=100)
    expected_dropped = {'high_missing', 'critical_missing', 'all_missing'}
    assert dropped_features == expected_dropped, f"Expected to drop {expected_dropped}, but dropped {dropped_features}"
    
    print("✓ Count-based threshold test passed")


def test_missing_value_report():
    """Test missing value report generation."""
    
    print("\n=== Test 4: Missing Value Report ===")
    
    test_data = create_test_data_with_varying_missing()
    
    config = {'filter_high_missing': True}
    generator = FeatureGenerator(config)
    
    # Generate report
    report = generator.get_missing_value_report(test_data)
    
    print("Missing value report:")
    print(f"  Total features: {report['total_features']}")
    print(f"  Features with missing: {report['features_with_missing']}")
    print(f"  Overall missing ratio: {report['overall_missing_ratio']:.1%}")
    
    print("\nDetailed feature analysis:")
    for feature, stats in report['missing_summary'].items():
        print(f"  {feature}: {stats['count']} ({stats['ratio']:.1%}) - {stats['severity']}")
    
    # Verify report accuracy
    assert report['total_features'] == 9, f"Expected 9 features, got {report['total_features']}"
    assert report['features_with_missing'] == 5, f"Expected 5 features with missing, got {report['features_with_missing']}"
    
    print("✓ Missing value report test passed")


def test_filtering_disabled():
    """Test with filtering disabled."""
    
    print("\n=== Test 5: Filtering Disabled ===")
    
    test_data = create_test_data_with_varying_missing()
    
    # Disable filtering
    config = {'filter_high_missing': False}
    generator = FeatureGenerator(config)
    
    filtered_data = generator.filter_high_missing_features(test_data)
    
    # Should return original data unchanged
    assert len(filtered_data.columns) == len(test_data.columns), "Data should be unchanged when filtering is disabled"
    assert list(filtered_data.columns) == list(test_data.columns), "Column order should be preserved"
    
    print("✓ Filtering disabled test passed")


def test_integration_with_real_data():
    """Test integration with real data processing."""
    
    print("\n=== Test 6: Integration Test ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config = {
            'output_path': temp_dir,
            'output_format': 'parquet',
            'filter_high_missing': True,
            'missing_threshold': 0.3,  # More conservative threshold
            'missing_threshold_mode': 'ratio'
        }
        
        generator = FeatureGenerator(config)
        
        # Get available symbols
        symbols = generator.get_available_symbols()
        
        if symbols:
            test_symbol = symbols[0]
            print(f"Testing with symbol: {test_symbol}")
            
            # Process the symbol (this will include missing value filtering)
            success = generator.process_symbol(test_symbol)
            
            if success:
                print("✓ Integration test completed successfully")
            else:
                print("⚠ Symbol processing failed, but this might be due to missing data")
        else:
            print("⚠ No symbols available for testing")


def main():
    """Run all missing value filtering tests."""
    
    print("Missing Value Filtering Tests")
    print("=" * 50)
    
    try:
        # Run tests
        test_missing_value_filtering_basic()
        test_different_thresholds()
        test_count_mode_threshold()
        test_missing_value_report()
        test_filtering_disabled()
        test_integration_with_real_data()
        
        print("\n" + "=" * 50)
        print("All missing value filtering tests completed successfully!")
        print("=" * 50)
        
    except Exception as e:
        print(f"\nTest failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
