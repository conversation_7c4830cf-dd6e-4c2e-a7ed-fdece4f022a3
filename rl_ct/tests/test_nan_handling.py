#!/usr/bin/env python3
"""
Test script for NaN handling functionality in FeatureGenerator.
"""

import sys
import tempfile
import numpy as np
import pandas as pd
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from rl_ct.utils.feature_generator import FeatureGenerator
from rl_ct.utils.logger import get_logger

logger = get_logger(__name__)


def create_test_data_with_nans():
    """Create test data with various types of NaN values."""
    
    # Create sample data with different feature types
    dates = pd.date_range('2023-01-01', periods=100, freq='1min')
    
    data = pd.DataFrame({
        'datatime': dates,
        'symbol': 'TEST#USDT:USDT',
        
        # Price features (should use forward fill)
        'open': np.random.randn(100) * 0.01 + 100,
        'close': np.random.randn(100) * 0.01 + 100,
        'high': np.random.randn(100) * 0.01 + 101,
        'low': np.random.randn(100) * 0.01 + 99,
        'vwap': np.random.randn(100) * 0.01 + 100,
        
        # Volume features (should use zero fill)
        'vol': np.random.randint(1000, 10000, 100),
        'val': np.random.randint(100000, 1000000, 100),
        
        # Technical indicators (should use forward fill)
        'rsi_7': np.random.randn(100) * 10 + 50,
        'sma_7': np.random.randn(100) * 0.01 + 100,
        'macd_12': np.random.randn(100) * 0.001,
        
        # Returns/ratios (should use zero fill)
        'returns': np.random.randn(100) * 0.001,
        'log_returns': np.random.randn(100) * 0.001,
        'pre_cr_1': np.random.randn(100) * 0.001,
        
        # Variance/volatility (should use forward fill)
        'var_5': np.random.randn(100) * 0.0001 + 0.001,
        'stddev_5': np.random.randn(100) * 0.01 + 0.1,
        
        # DL model outputs (should use forward fill)
        'pred_model_score': np.random.randn(100) * 0.1,
        'pred_model_action': np.random.choice([0, 1, 2], 100),
    })
    
    # Introduce NaN values in different patterns
    
    # Random NaN values (10% of data)
    for col in ['open', 'close', 'vol', 'rsi_7', 'returns']:
        nan_indices = np.random.choice(100, size=10, replace=False)
        data.loc[nan_indices, col] = np.nan
    
    # Consecutive NaN values at the beginning
    data.loc[0:4, 'sma_7'] = np.nan
    data.loc[0:2, 'macd_12'] = np.nan
    
    # Consecutive NaN values in the middle
    data.loc[45:50, 'val'] = np.nan
    data.loc[60:65, 'log_returns'] = np.nan
    
    # NaN values at the end
    data.loc[95:99, 'var_5'] = np.nan
    data.loc[97:99, 'pred_model_score'] = np.nan
    
    return data


def test_nan_handling_basic():
    """Test basic NaN handling functionality."""
    
    print("=== Test 1: Basic NaN Handling ===")
    
    # Create test data
    test_data = create_test_data_with_nans()
    
    # Count original NaN values
    original_nan_counts = test_data.isna().sum()
    total_original_nans = original_nan_counts.sum()
    
    print(f"Original data shape: {test_data.shape}")
    print(f"Total NaN values before processing: {total_original_nans}")
    print("NaN counts by column:")
    for col, count in original_nan_counts.items():
        if count > 0:
            print(f"  {col}: {count}")
    
    # Create feature generator with NaN handling enabled
    config = {'handle_nan': True}
    generator = FeatureGenerator(config)
    
    # Apply NaN handling
    cleaned_data = generator.handle_nan_values(test_data)
    
    # Count remaining NaN values
    cleaned_nan_counts = cleaned_data.isna().sum()
    total_cleaned_nans = cleaned_nan_counts.sum()
    
    print(f"\nAfter NaN handling:")
    print(f"Total NaN values: {total_cleaned_nans}")
    
    if total_cleaned_nans > 0:
        print("Remaining NaN counts by column:")
        for col, count in cleaned_nan_counts.items():
            if count > 0:
                print(f"  {col}: {count}")
    
    # Verify no NaN values remain
    assert total_cleaned_nans == 0, f"Expected 0 NaN values, but found {total_cleaned_nans}"
    print("✓ All NaN values successfully handled")
    
    return test_data, cleaned_data


def test_nan_strategies():
    """Test different NaN handling strategies."""
    
    print("\n=== Test 2: NaN Handling Strategies ===")
    
    # Create simple test data
    data = pd.DataFrame({
        'datatime': pd.date_range('2023-01-01', periods=10, freq='1min'),
        'price_feature': [100, np.nan, np.nan, 103, 104, np.nan, 106, 107, np.nan, 109],
        'volume_feature': [1000, np.nan, np.nan, 1300, 1400, np.nan, 1600, 1700, np.nan, 1900],
        'returns_feature': [0.01, np.nan, np.nan, -0.02, 0.03, np.nan, -0.01, 0.02, np.nan, 0.01]
    })
    
    print("Original data:")
    print(data)
    
    config = {'handle_nan': True}
    generator = FeatureGenerator(config)
    
    cleaned_data = generator.handle_nan_values(data)
    
    print("\nCleaned data:")
    print(cleaned_data)
    
    # Verify strategies
    # Price feature should use forward fill
    expected_price = [100, 100, 100, 103, 104, 104, 106, 107, 107, 109]
    assert list(cleaned_data['price_feature']) == expected_price, "Price feature forward fill failed"
    print("✓ Price feature forward fill strategy working correctly")
    
    # Volume feature should use zero fill
    expected_volume = [1000, 0, 0, 1300, 1400, 0, 1600, 1700, 0, 1900]
    assert list(cleaned_data['volume_feature']) == expected_volume, "Volume feature zero fill failed"
    print("✓ Volume feature zero fill strategy working correctly")
    
    # Returns feature should use zero fill
    expected_returns = [0.01, 0, 0, -0.02, 0.03, 0, -0.01, 0.02, 0, 0.01]
    assert list(cleaned_data['returns_feature']) == expected_returns, "Returns feature zero fill failed"
    print("✓ Returns feature zero fill strategy working correctly")


def test_custom_nan_strategies():
    """Test custom NaN handling strategies."""
    
    print("\n=== Test 3: Custom NaN Strategies ===")
    
    # Create test data
    data = pd.DataFrame({
        'datatime': pd.date_range('2023-01-01', periods=10, freq='1min'),
        'custom_feature': [1, np.nan, np.nan, 4, 5, np.nan, 7, 8, np.nan, 10]
    })
    
    # Test with custom strategy override
    config = {
        'handle_nan': True,
        'nan_strategy_override': {
            'custom_feature': 'interpolate'
        }
    }
    
    generator = FeatureGenerator(config)
    cleaned_data = generator.handle_nan_values(data)
    
    print("Original data:")
    print(data['custom_feature'].tolist())
    print("Cleaned data (interpolate strategy):")
    print(cleaned_data['custom_feature'].tolist())
    
    # Verify interpolation worked
    assert not cleaned_data['custom_feature'].isna().any(), "Custom interpolate strategy failed"
    print("✓ Custom interpolate strategy working correctly")


def test_nan_handling_disabled():
    """Test with NaN handling disabled."""
    
    print("\n=== Test 4: NaN Handling Disabled ===")
    
    # Create test data with NaN values
    data = pd.DataFrame({
        'datatime': pd.date_range('2023-01-01', periods=5, freq='1min'),
        'test_feature': [1, np.nan, 3, np.nan, 5]
    })
    
    # Create generator with NaN handling disabled
    config = {'handle_nan': False}
    generator = FeatureGenerator(config)
    
    # NaN handling should not be applied
    result_data = generator.handle_nan_values(data)  # This should still work but not be called in process_symbol
    
    original_nans = data.isna().sum().sum()
    result_nans = result_data.isna().sum().sum()
    
    print(f"Original NaN count: {original_nans}")
    print(f"Result NaN count: {result_nans}")
    
    # When handle_nan is False, the method still processes but won't be called in the main flow
    assert result_nans == 0, "NaN handling method should still work when called directly"
    print("✓ NaN handling method works correctly even when disabled in config")


def test_integration_with_feature_generator():
    """Test NaN handling integration with the full feature generation process."""
    
    print("\n=== Test 5: Integration Test ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config = {
            'output_path': temp_dir,
            'output_format': 'parquet',
            'handle_nan': True
        }
        
        generator = FeatureGenerator(config)
        
        # Get available symbols
        symbols = generator.get_available_symbols()
        
        if symbols:
            test_symbol = symbols[0]
            print(f"Testing with symbol: {test_symbol}")
            
            # Process the symbol (this will include NaN handling)
            success = generator.process_symbol(test_symbol)
            
            if success:
                # Load the processed data and check for NaN values
                from rl_ct.utils.data_loaders.disk_loader import DiskDataLoader
                
                loader = DiskDataLoader(
                    data_dir=temp_dir,
                    dataset_name=test_symbol,
                    file_format='parquet'
                )
                
                price_data, feature_data = loader.load_data()
                
                price_nans = np.isnan(price_data).sum()
                feature_nans = np.isnan(feature_data).sum()
                
                print(f"Price data NaN count: {price_nans}")
                print(f"Feature data NaN count: {feature_nans}")
                
                # Note: Some NaN values might remain due to the nature of the original data
                # but they should be significantly reduced
                print("✓ Integration test completed successfully")
            else:
                print("⚠ Symbol processing failed, but this might be due to missing data")
        else:
            print("⚠ No symbols available for testing")


def main():
    """Run all NaN handling tests."""
    
    print("NaN Handling Tests")
    print("=" * 50)
    
    try:
        # Run tests
        test_nan_handling_basic()
        test_nan_strategies()
        test_custom_nan_strategies()
        test_nan_handling_disabled()
        test_integration_with_feature_generator()
        
        print("\n" + "=" * 50)
        print("All NaN handling tests completed successfully!")
        print("=" * 50)
        
    except Exception as e:
        print(f"\nTest failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
