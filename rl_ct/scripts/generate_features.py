#!/usr/bin/env python3
"""
Command-line tool for generating features in DiskDataLoader format.

This script converts existing data features from deep learning models and 
price-volume data into the format required by DiskDataLoader.
"""

import argparse
import json
import logging
import sys
from pathlib import Path
from typing import List, Optional

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from rl_ct.utils.feature_generator import FeatureGenerator
from rl_ct.utils.logger import get_logger

logger = get_logger(__name__)


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Generate features in DiskDataLoader format",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Process all symbols with default settings
  python generate_features.py --all

  # Process specific symbols
  python generate_features.py --symbols BTC#USDT:USDT ETH#USDT:USDT

  # Use custom paths and output format
  python generate_features.py --all --output-path data/my_processed --format npy

  # Process with custom deep learning model paths
  python generate_features.py --all --dl-paths data_snap/model1 data_snap/model2

  # Show available symbols and features
  python generate_features.py --summary

  # Process with feature selection
  python generate_features.py --all --features close open high low rsi_7 macd_12
        """
    )
    
    # Data source configuration
    parser.add_argument(
        '--dl-paths', 
        nargs='+',
        default=[
            'data_snap/pred_model_5min_gain_v5_432882_online',
            'data_snap/pred_res_online_feat_kline_1m_with_vola_to_20210301_36symbols_v3',
            'data_snap/pred_res_online_feat_kline_1m_with_vola_to_20210301_36symbols_v9'
        ],
        help='Paths to deep learning model output directories'
    )
    
    parser.add_argument(
        '--pv-path',
        default='data_snap/portfolio_v1',
        help='Path to price-volume data directory'
    )
    
    # Output configuration
    parser.add_argument(
        '--output-path',
        default='data/processed',
        help='Output directory for processed data'
    )
    
    parser.add_argument(
        '--format',
        choices=['npy', 'csv', 'parquet'],
        default='parquet',
        help='Output file format'
    )
    
    parser.add_argument(
        '--dataset-name',
        help='Custom dataset name (default: auto-generated based on config)'
    )
    
    # Symbol selection
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument(
        '--all',
        action='store_true',
        help='Process all available symbols'
    )
    
    group.add_argument(
        '--symbols',
        nargs='+',
        help='Specific symbols to process'
    )
    
    group.add_argument(
        '--summary',
        action='store_true',
        help='Show summary of available data and exit'
    )
    
    # Feature selection
    parser.add_argument(
        '--features',
        nargs='+',
        help='Specific features to include (default: all features)'
    )
    
    # NaN handling options
    parser.add_argument(
        '--handle-nan',
        action='store_true',
        default=True,
        help='Enable NaN value handling (default: True)'
    )

    parser.add_argument(
        '--no-handle-nan',
        action='store_true',
        help='Disable NaN value handling'
    )

    # Missing value filtering options
    parser.add_argument(
        '--filter-missing',
        action='store_true',
        default=True,
        help='Enable filtering of high-missing features (default: True)'
    )

    parser.add_argument(
        '--no-filter-missing',
        action='store_true',
        help='Disable filtering of high-missing features'
    )

    parser.add_argument(
        '--missing-threshold',
        type=float,
        default=0.5,
        help='Threshold for filtering high-missing features (default: 0.5)'
    )

    parser.add_argument(
        '--missing-threshold-mode',
        choices=['ratio', 'count'],
        default='ratio',
        help='Threshold mode: ratio (0-1) or count (absolute number)'
    )

    # Other options
    parser.add_argument(
        '--config',
        help='Path to JSON configuration file'
    )

    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose logging'
    )

    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Show what would be processed without actually processing'
    )
    
    return parser.parse_args()


def load_config(config_path: Optional[str]) -> dict:
    """Load configuration from JSON file."""
    if not config_path:
        return {}
    
    try:
        with open(config_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Failed to load config from {config_path}: {e}")
        return {}


def create_config(args) -> dict:
    """Create configuration dictionary from arguments."""
    config = load_config(args.config)
    
    # Override with command line arguments
    config.update({
        'dl_model_paths': args.dl_paths,
        'price_volume_path': args.pv_path,
        'output_path': args.output_path,
        'output_format': args.format,
        'selected_features': args.features,
        'selected_symbols': args.symbols if not args.all else None,
        'handle_nan': not args.no_handle_nan if args.no_handle_nan else args.handle_nan,
        'filter_high_missing': not args.no_filter_missing if args.no_filter_missing else args.filter_missing,
        'missing_threshold': args.missing_threshold,
        'missing_threshold_mode': args.missing_threshold_mode
    })
    
    return config


def show_summary(generator: FeatureGenerator):
    """Show summary of available data."""
    summary = generator.get_processing_summary()
    
    print("=== Feature Generation Summary ===")
    print(f"Price-Volume Path: {summary['price_volume_path']}")
    print(f"Deep Learning Model Paths:")
    for path in summary['dl_model_paths']:
        print(f"  - {path}")
    print(f"Output Path: {summary['output_path']}")
    print(f"Output Format: {summary['output_format']}")
    print(f"Total Available Symbols: {summary['total_symbols']}")
    
    if summary['selected_symbols']:
        print(f"Selected Symbols: {len(summary['selected_symbols'])}")
        for symbol in summary['selected_symbols'][:10]:  # Show first 10
            print(f"  - {symbol}")
        if len(summary['selected_symbols']) > 10:
            print(f"  ... and {len(summary['selected_symbols']) - 10} more")
    else:
        print("Selected Symbols: All available")
        print("Available Symbols (first 10):")
        for symbol in summary['available_symbols'][:10]:
            print(f"  - {symbol}")
        if len(summary['available_symbols']) > 10:
            print(f"  ... and {len(summary['available_symbols']) - 10} more")
    
    if summary['selected_features']:
        print(f"Selected Features: {len(summary['selected_features'])}")
        for feature in summary['selected_features'][:10]:  # Show first 10
            print(f"  - {feature}")
        if len(summary['selected_features']) > 10:
            print(f"  ... and {len(summary['selected_features']) - 10} more")
    else:
        print("Selected Features: All available")
    
    # Show sample features
    if 'sample_features' in summary:
        sample_features = summary['sample_features']
        print(f"\nSample Features (from {summary['available_symbols'][0]}):")
        
        if sample_features['price_volume']:
            print(f"  Price-Volume Features: {len(sample_features['price_volume'])}")
            print(f"    Examples: {sample_features['price_volume'][:5]}")
        
        for model_name, features in sample_features['dl_models'].items():
            print(f"  {model_name}: {len(features)} features")
            print(f"    Examples: {features[:5]}")


def main():
    """Main function."""
    args = parse_arguments()
    
    # Configure logging
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Create configuration
    config = create_config(args)
    
    # Create feature generator
    generator = FeatureGenerator(config)
    
    # Handle summary request
    if args.summary:
        show_summary(generator)
        return
    
    # Show configuration in dry-run mode
    if args.dry_run:
        print("=== Dry Run Mode ===")
        show_summary(generator)
        print("\nWould process the above configuration.")
        return
    
    # Process data
    logger.info("Starting feature generation...")
    
    if args.all:
        results = generator.process_all_symbols()
    else:
        results = {}
        for symbol in args.symbols:
            results[symbol] = generator.process_symbol(symbol)
    
    # Show results
    successful = sum(1 for success in results.values() if success)
    total = len(results)
    
    print(f"\n=== Processing Results ===")
    print(f"Successfully processed: {successful}/{total} symbols")
    
    if successful < total:
        print("\nFailed symbols:")
        for symbol, success in results.items():
            if not success:
                print(f"  - {symbol}")
    
    if successful > 0:
        print(f"\nProcessed data saved to: {config['output_path']}")
        print(f"Format: {config['output_format']}")
    
    logger.info("Feature generation complete")


if __name__ == "__main__":
    main()
