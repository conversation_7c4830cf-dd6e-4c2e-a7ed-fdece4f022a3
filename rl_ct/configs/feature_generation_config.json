{"description": "Configuration for feature generation", "dl_model_paths": ["data_snap/pred_model_5min_gain_v5_432882_online", "data_snap/pred_res_online_feat_kline_1m_with_vola_to_20210301_36symbols_v3", "data_snap/pred_res_online_feat_kline_1m_with_vola_to_20210301_36symbols_v9"], "price_volume_path": "data_snap/portfolio_v1", "output_path": "data/processed", "output_format": "parquet", "selected_features": null, "selected_symbols": null, "feature_presets": {"basic_price": ["open", "close", "high", "low", "vol", "val", "vwap"], "technical_indicators": ["rsi_7", "rsi_9", "rsi_25", "sma_7", "sma_9", "sma_25", "ema_7", "ema_9", "ema_25", "macd_12", "macd_26", "macd_9", "bBands_1", "bBands_2", "bBands_3", "atr_14", "adx_15", "cci_14"], "volatility_features": ["var_5", "var_10", "var_15", "var_30", "var_60", "var_120", "stddev_5", "stddev_10", "stddev_15", "stddev_30", "stddev_60", "stddev_120", "ret_vol"], "momentum_features": ["mom_10", "roc_10", "rocp_10", "rocr_10", "pre_cr_1", "pre_cr_3", "pre_cr_5", "pre_cr_15", "pre_cr_30", "pre_cr_60", "pre_cr_120", "pre_cr_240", "pre_cr_480"], "dl_model_features": ["score", "action"]}, "symbol_groups": {"major_crypto": ["BTC#USDT:USDT", "ETH#USDT:USDT", "BNB#USDT:USDT", "ADA#USDT:USDT", "SOL#USDT:USDT"], "altcoins": ["DOGE#USDT:USDT", "SHIB#USDT:USDT", "AVAX#USDT:USDT", "MATIC#USDT:USDT", "DOT#USDT:USDT"]}, "processing_options": {"handle_missing_data": "forward_fill", "normalize_features": false, "remove_outliers": false, "min_data_points": 1000}, "nan_handling": {"handle_nan": true, "nan_strategy_override": {"vol": "zero_fill", "val": "zero_fill", "returns": "zero_fill", "log_returns": "zero_fill", "target_returns": "zero_fill"}, "default_strategies": {"price_features": "forward_fill", "volume_features": "zero_fill", "technical_indicators": "forward_fill", "returns_ratios": "zero_fill", "dl_model_outputs": "forward_fill"}}, "missing_value_filtering": {"filter_high_missing": true, "missing_threshold": 0.5, "missing_threshold_mode": "ratio", "threshold_options": {"conservative": 0.1, "moderate": 0.3, "permissive": 0.7}}}