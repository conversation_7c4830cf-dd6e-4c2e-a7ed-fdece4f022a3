"""
Feature generator for converting existing data to DiskDataLoader format.

This module provides functionality to convert existing data features from:
1. Deep learning model outputs (prediction scores)
2. Price-volume features (technical indicators)

Into the format required by DiskDataLoader (prices.{format} and features.{format}).
"""

import json
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime

from rl_ct.utils.logger import get_logger

logger = get_logger(__name__)


class FeatureGenerator:
    """
    Convert existing data features to DiskDataLoader format.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize feature generator.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config or {}
        
        # Default paths
        self.dl_model_paths = self.config.get('dl_model_paths', [
            'data_snap/pred_model_5min_gain_v5_432882_online',
            'data_snap/pred_res_online_feat_kline_1m_with_vola_to_20210301_36symbols_v3',
            'data_snap/pred_res_online_feat_kline_1m_with_vola_to_20210301_36symbols_v9'
        ])
        
        self.price_volume_path = self.config.get('price_volume_path', 'data_snap/portfolio_v1')
        self.output_path = self.config.get('output_path', 'data/processed')
        self.output_format = self.config.get('output_format', 'parquet')
        
        # Feature selection
        self.selected_features = self.config.get('selected_features', None)  # None means all features
        self.selected_symbols = self.config.get('selected_symbols', None)    # None means all symbols

        # NaN handling configuration
        self.handle_nan = self.config.get('handle_nan', True)  # Enable NaN handling by default
        self.nan_strategy_override = self.config.get('nan_strategy_override', {})  # Custom strategies for specific columns

        # Missing value filtering configuration
        self.filter_high_missing = self.config.get('filter_high_missing', True)  # Enable filtering by default
        self.missing_threshold = self.config.get('missing_threshold', 0.5)  # Default 50% threshold
        self.missing_threshold_mode = self.config.get('missing_threshold_mode', 'ratio')  # 'ratio' or 'count'
        
    def get_available_symbols(self) -> List[str]:
        """
        Get list of available symbols from price-volume data.
        
        Returns:
            List of available symbol names
        """
        symbols = []
        price_volume_dir = Path(self.price_volume_path)
        
        if price_volume_dir.exists():
            for file_path in price_volume_dir.iterdir():
                if file_path.is_file():
                    symbols.append(file_path.name)
                    
        return sorted(symbols)
    
    def get_available_features(self, symbol: str) -> Dict[str, List[str]]:
        """
        Get available features for a given symbol.
        
        Args:
            symbol: Symbol name
            
        Returns:
            Dictionary with feature categories and their column names
        """
        features = {
            'price_volume': [],
            'dl_models': {}
        }
        
        # Get price-volume features
        pv_file = Path(self.price_volume_path) / symbol
        if pv_file.exists():
            try:
                df = pd.read_parquet(pv_file)
                features['price_volume'] = df.columns.tolist()
            except Exception as e:
                logger.warning(f"Failed to read price-volume data for {symbol}: {e}")
        
        # Get deep learning model features
        for model_path in self.dl_model_paths:
            model_name = Path(model_path).name
            model_file = Path(model_path) / symbol
            if model_file.exists():
                try:
                    df = pd.read_parquet(model_file)
                    features['dl_models'][model_name] = df.columns.tolist()
                except Exception as e:
                    logger.warning(f"Failed to read DL model data for {symbol} from {model_path}: {e}")
        
        return features
    
    def load_symbol_data(self, symbol: str) -> Tuple[Optional[pd.DataFrame], Dict[str, pd.DataFrame]]:
        """
        Load all data for a given symbol.
        
        Args:
            symbol: Symbol name
            
        Returns:
            Tuple of (price_volume_data, dl_model_data_dict)
        """
        # Load price-volume data
        pv_data = None
        pv_file = Path(self.price_volume_path) / symbol
        if pv_file.exists():
            try:
                pv_data = pd.read_parquet(pv_file)
                # Ensure datetime column is datetime type
                if 'datatime' in pv_data.columns:
                    pv_data['datatime'] = pd.to_datetime(pv_data['datatime'])
                logger.info(f"Loaded price-volume data for {symbol}: {pv_data.shape}")
            except Exception as e:
                logger.error(f"Failed to load price-volume data for {symbol}: {e}")
        
        # Load deep learning model data
        dl_data = {}
        for model_path in self.dl_model_paths:
            model_name = Path(model_path).name
            model_name = model_name.split('_')[-1]
            model_file = Path(model_path) / symbol
            if model_file.exists():
                try:
                    df = pd.read_parquet(model_file)
                    # Ensure datetime column is datetime type
                    if 'datatime' in df.columns:
                        df['datatime'] = pd.to_datetime(df['datatime'])
                    dl_data[model_name] = df
                    logger.info(f"Loaded DL model data for {symbol} from {model_name}: {df.shape}")
                except Exception as e:
                    logger.error(f"Failed to load DL model data for {symbol} from {model_path}: {e}")
        
        return pv_data, dl_data
    
    def merge_data(self, pv_data: pd.DataFrame, dl_data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """
        Merge price-volume data with deep learning model data.
        
        Args:
            pv_data: Price-volume DataFrame
            dl_data: Dictionary of deep learning model DataFrames
            
        Returns:
            Merged DataFrame
        """
        if pv_data is None:
            logger.error("No price-volume data to merge")
            return pd.DataFrame()
        
        merged_data = pv_data.copy()
        
        # Merge each DL model data
        for model_name, df in dl_data.items():
            if df is not None and not df.empty:
                # Prepare DL data for merging
                dl_merge_data = df.copy()
                
                # Rename columns to avoid conflicts (except datetime and symbol)
                rename_cols = {}
                for col in dl_merge_data.columns:
                    if col not in ['datatime', 'symbol']:
                        rename_cols[col] = f"{model_name}_{col}"
                
                if rename_cols:
                    dl_merge_data = dl_merge_data.rename(columns=rename_cols)
                
                # Merge on datetime and symbol
                merge_cols = ['datatime']
                if 'symbol' in merged_data.columns and 'symbol' in dl_merge_data.columns:
                    merge_cols.append('symbol')
                
                merged_data = pd.merge(
                    merged_data, 
                    dl_merge_data, 
                    on=merge_cols, 
                    how='left',
                    suffixes=('', f'_{model_name}')
                )
                
                logger.info(f"Merged data with {model_name}: {merged_data.shape}")
        
        return merged_data
    
    def extract_price_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Extract price-related features for the prices file.
        
        Args:
            data: Merged data DataFrame
            
        Returns:
            DataFrame with price features
        """
        price_columns = ['datatime', 'symbol', 'open', 'close', 'high', 'low', 'vol', 'val', 'vwap']
        
        # Find available price columns
        available_price_cols = [col for col in price_columns if col in data.columns]
        
        if not available_price_cols:
            logger.warning("No price columns found in data")
            return pd.DataFrame()
        
        price_data = data[available_price_cols].copy()
        
        # Add basic price features if not present
        if 'close' in price_data.columns:
            if 'returns' not in price_data.columns:
                price_data['returns'] = price_data['close'].pct_change()
            
            if 'log_returns' not in price_data.columns:
                price_data['log_returns'] = np.log(price_data['close'] / price_data['close'].shift(1))
        
        return price_data
    
    def extract_feature_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Extract feature data (excluding basic price columns).
        
        Args:
            data: Merged data DataFrame
            
        Returns:
            DataFrame with feature data
        """
        # Basic price columns to exclude from features
        basic_price_cols = ['open', 'close', 'high', 'low', 'vol', 'val']
        
        # Keep datetime and symbol for indexing
        keep_cols = ['datatime', 'symbol']
        
        # Select feature columns
        feature_cols = keep_cols.copy()
        
        for col in data.columns:
            if col not in basic_price_cols and col not in keep_cols:
                # Apply feature selection if specified
                if self.selected_features is None or col in self.selected_features:
                    feature_cols.append(col)
        
        if len(feature_cols) <= len(keep_cols):
            logger.warning("No feature columns selected")
            return pd.DataFrame()
        
        feature_data = data[feature_cols].copy()
        
        return feature_data

    def handle_nan_values(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Handle NaN values in the data based on feature types.

        Different strategies for different feature types:
        - Price features: forward fill
        - Volume features: fill with 0
        - Technical indicators: forward fill
        - Deep learning model outputs: forward fill
        - Returns/ratios: fill with 0

        Args:
            data: Input DataFrame with potential NaN values

        Returns:
            DataFrame with NaN values handled
        """
        data_cleaned = data.copy()

        # Define feature categories and their fill strategies
        feature_categories = {
            'price_features': {
                'patterns': ['open', 'close', 'high', 'low', 'vwap', 'avgPrice', 'medPrice', 'typPrice', 'wclPrice'],
                'strategy': 'forward_fill'
            },
            'volume_features': {
                'patterns': ['vol', 'val', 'ad', 'obv'],
                'strategy': 'zero_fill'
            },
            'technical_indicators': {
                'patterns': ['sma_', 'ema_', 'wma_', 'dema_', 'tema_', 'trima_', 'kama_', 't3_', 'ma_',
                           'rsi_', 'bBands_', 'stochf_', 'stoch_', 'stochRsi_', 'trix_', 'correl_',
                           'linearreg_', 'bop', 'cci_', 'trange', 'plus_dm_', 'plus_di_', 'minus_dm_',
                           'minus_di_', 'dx_', 'adx_', 'adxr_', 'cmo_', 'macd_', 'midPrice_', 'midPoint_',
                           'mom_', 'roc_', 'rocp_', 'rocr_', 'ppo_', 'aroon_', 'ultOsc_', 'willr_',
                           'atr_', 'natr_', 'highest_', 'lowest_', 'beta'],
                'strategy': 'forward_fill'
            },
            'returns_ratios': {
                'patterns': ['returns', 'log_returns', '_cr', 'pre_cr_', 'pre_vol_cr_', 'open_cr',
                           'high_cr', 'low_cr', 'ret_vol', 'target_returns', 'win_ratio', 'rocr100'],
                'strategy': 'zero_fill'
            },
            'variance_volatility': {
                'patterns': ['var_', 'stddev_'],
                'strategy': 'forward_fill'
            },
            'dl_model_outputs': {
                'patterns': ['_score', '_action', '_exchange', '_extra', '_createtime'],
                'strategy': 'forward_fill'
            },
            'market_indicators': {
                'patterns': ['avg_cr_dapan_', 'close_index_diff', 'funding_rate_min'],
                'strategy': 'forward_fill'
            }
        }

        # Process each column
        for col in data_cleaned.columns:
            if col in ['datatime', 'symbol']:
                continue

            if data_cleaned[col].isna().any():
                # Check for custom strategy override first
                if col in self.nan_strategy_override:
                    strategy = self.nan_strategy_override[col]
                else:
                    # Determine the appropriate strategy for this column
                    strategy = self._get_fill_strategy(col, feature_categories)

                # Apply the strategy
                nan_count_before = data_cleaned[col].isna().sum()

                if strategy == 'forward_fill':
                    data_cleaned[col] = data_cleaned[col].ffill()
                    # If still NaN at the beginning, fill with 0
                    data_cleaned[col] = data_cleaned[col].fillna(0)
                elif strategy == 'zero_fill':
                    data_cleaned[col] = data_cleaned[col].fillna(0)
                elif strategy == 'backward_fill':
                    data_cleaned[col] = data_cleaned[col].bfill()
                    # If still NaN at the end, fill with 0
                    data_cleaned[col] = data_cleaned[col].fillna(0)
                elif strategy == 'interpolate':
                    data_cleaned[col] = data_cleaned[col].interpolate()
                    # Fill remaining NaN with forward fill
                    data_cleaned[col] = data_cleaned[col].ffill()
                    data_cleaned[col] = data_cleaned[col].fillna(0)
                else:
                    # Default: forward fill
                    data_cleaned[col] = data_cleaned[col].ffill()
                    data_cleaned[col] = data_cleaned[col].fillna(0)

                nan_count_after = data_cleaned[col].isna().sum()
                logger.debug(f"Column '{col}': {nan_count_before} NaN -> {nan_count_after} NaN (strategy: {strategy})")

        # Log summary
        original_nan_count = data.isna().sum().sum()
        cleaned_nan_count = data_cleaned.isna().sum().sum()

        if original_nan_count > 0:
            logger.info(f"NaN handling: {original_nan_count} NaN values -> {cleaned_nan_count} NaN values")

        return data_cleaned

    def _get_fill_strategy(self, column_name: str, feature_categories: dict) -> str:
        """
        Determine the appropriate fill strategy for a column based on its name.

        Args:
            column_name: Name of the column
            feature_categories: Dictionary of feature categories and strategies

        Returns:
            Fill strategy ('forward_fill', 'zero_fill', or 'default')
        """
        column_lower = column_name.lower()

        for config in feature_categories.values():
            patterns = config['patterns']
            strategy = config['strategy']

            # Check if column matches any pattern in this category
            for pattern in patterns:
                if pattern.lower() in column_lower:
                    return strategy

        # Default strategy if no pattern matches
        return 'forward_fill'

    def filter_high_missing_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Filter out features with missing values above the threshold.

        Args:
            data: Input DataFrame

        Returns:
            DataFrame with high-missing features removed
        """
        if not self.filter_high_missing:
            return data

        if data.empty:
            return data

        # Calculate missing statistics for each column
        missing_stats = {}
        columns_to_drop = []
        total_rows = len(data)

        for col in data.columns:
            if col in ['datatime', 'symbol']:
                continue

            missing_count = data[col].isna().sum()
            missing_ratio = missing_count / total_rows if total_rows > 0 else 0

            missing_stats[col] = {
                'count': missing_count,
                'ratio': missing_ratio
            }

            # Determine if column should be dropped
            should_drop = False

            if self.missing_threshold_mode == 'ratio':
                should_drop = missing_ratio > self.missing_threshold
                threshold_desc = f"{self.missing_threshold:.1%}"
            else:  # count mode
                should_drop = missing_count > self.missing_threshold
                threshold_desc = f"{int(self.missing_threshold)} values"

            if should_drop:
                columns_to_drop.append(col)
                logger.warning(
                    f"Dropping feature '{col}' due to high missing values: "
                    f"{missing_count} ({missing_ratio:.1%}) > threshold ({threshold_desc})"
                )

        # Drop high-missing columns
        if columns_to_drop:
            filtered_data = data.drop(columns=columns_to_drop)
            logger.info(
                f"Filtered out {len(columns_to_drop)} features with high missing values. "
                f"Remaining features: {len(filtered_data.columns) - 2}"  # -2 for datatime and symbol
            )

            # Log summary statistics
            if missing_stats:
                high_missing_features = [col for col in columns_to_drop]
                if len(high_missing_features) <= 10:
                    logger.info(f"Dropped features: {high_missing_features}")
                else:
                    logger.info(f"Dropped features (first 10): {high_missing_features[:10]} ...")
        else:
            filtered_data = data
            logger.info("No features filtered due to missing values")

        return filtered_data

    def get_missing_value_report(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        Generate a detailed report of missing values in the data.

        Args:
            data: Input DataFrame

        Returns:
            Dictionary containing missing value statistics
        """
        if data.empty:
            return {'total_features': 0, 'features_with_missing': 0, 'missing_summary': {}}

        total_rows = len(data)
        feature_columns = [col for col in data.columns if col not in ['datatime', 'symbol']]

        missing_summary = {}
        features_with_missing = 0
        total_missing_values = 0

        for col in feature_columns:
            missing_count = data[col].isna().sum()
            missing_ratio = missing_count / total_rows if total_rows > 0 else 0

            if missing_count > 0:
                features_with_missing += 1
                total_missing_values += missing_count

                missing_summary[col] = {
                    'count': int(missing_count),
                    'ratio': float(missing_ratio),
                    'severity': self._get_missing_severity(missing_ratio)
                }

        return {
            'total_features': len(feature_columns),
            'total_rows': total_rows,
            'features_with_missing': features_with_missing,
            'total_missing_values': int(total_missing_values),
            'overall_missing_ratio': float(total_missing_values / (len(feature_columns) * total_rows)) if feature_columns and total_rows > 0 else 0,
            'missing_summary': missing_summary,
            'threshold': self.missing_threshold,
            'threshold_mode': self.missing_threshold_mode
        }

    def _get_missing_severity(self, missing_ratio: float) -> str:
        """
        Categorize missing value severity.

        Args:
            missing_ratio: Ratio of missing values (0-1)

        Returns:
            Severity level string
        """
        if missing_ratio == 0:
            return 'none'
        elif missing_ratio <= 0.05:
            return 'low'
        elif missing_ratio <= 0.2:
            return 'moderate'
        elif missing_ratio <= 0.5:
            return 'high'
        else:
            return 'critical'

    def save_processed_data(self, symbol: str, price_data: pd.DataFrame, feature_data: pd.DataFrame) -> None:
        """
        Save processed data in DiskDataLoader format.

        Args:
            symbol: Symbol name
            price_data: Price data DataFrame
            feature_data: Feature data DataFrame
        """
        # Create output directory
        output_dir = Path(self.output_path) / symbol
        output_dir.mkdir(parents=True, exist_ok=True)

        # Convert to numpy arrays (excluding datetime and symbol columns)
        price_array = self._dataframe_to_array(price_data)
        feature_array = self._dataframe_to_array(feature_data)

        # Save price data
        price_file = output_dir / f"prices.{self.output_format}"
        self._save_array(price_array, price_file, price_data)

        # Save feature data
        feature_file = output_dir / f"features.{self.output_format}"
        self._save_array(feature_array, feature_file, feature_data)

        # Save metadata
        metadata = {
            'symbol': symbol,
            'price_columns': [col for col in price_data.columns if col not in ['datatime', 'symbol']],
            'feature_columns': [col for col in feature_data.columns if col not in ['datatime', 'symbol']],
            'price_shape': price_array.shape,
            'feature_shape': feature_array.shape,
            'generated_at': datetime.now().isoformat(),
            'config': self.config
        }

        metadata_file = output_dir / "metadata.json"
        with open(metadata_file, 'w') as f:
            json.dump(metadata, f, indent=2)

        logger.info(f"Saved processed data for {symbol} to {output_dir}")
        logger.info(f"Price data shape: {price_array.shape}, Feature data shape: {feature_array.shape}")

    def _dataframe_to_array(self, df: pd.DataFrame) -> np.ndarray:
        """
        Convert DataFrame to numpy array, excluding datetime and symbol columns.

        Args:
            df: Input DataFrame

        Returns:
            Numpy array
        """
        if df.empty:
            return np.array([])

        # Select numeric columns only
        numeric_cols = []
        for col in df.columns:
            if col not in ['datatime', 'symbol']:
                if pd.api.types.is_numeric_dtype(df[col]):
                    numeric_cols.append(col)

        if not numeric_cols:
            logger.warning("No numeric columns found for array conversion")
            return np.array([])

        return df[numeric_cols].values

    def _save_array(self, array: np.ndarray, file_path: Path, original_df: pd.DataFrame) -> None:
        """
        Save numpy array to file.

        Args:
            array: Numpy array to save
            file_path: Output file path
            original_df: Original DataFrame for column information
        """
        if array.size == 0:
            logger.warning(f"Empty array, skipping save to {file_path}")
            return

        if self.output_format == "npy":
            np.save(file_path, array)
        elif self.output_format == "csv":
            # Create DataFrame with numeric columns only
            numeric_cols = [col for col in original_df.columns
                          if col not in ['datatime', 'symbol'] and pd.api.types.is_numeric_dtype(original_df[col])]
            df = pd.DataFrame(array, columns=numeric_cols)
            df.to_csv(file_path, index=False)
        elif self.output_format == "parquet":
            # Create DataFrame with numeric columns only
            numeric_cols = [col for col in original_df.columns
                          if col not in ['datatime', 'symbol'] and pd.api.types.is_numeric_dtype(original_df[col])]
            df = pd.DataFrame(array, columns=numeric_cols)
            df.to_parquet(file_path, index=False)
        else:
            raise ValueError(f"Unsupported output format: {self.output_format}")

    def process_symbol(self, symbol: str) -> bool:
        """
        Process a single symbol.

        Args:
            symbol: Symbol name

        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info(f"Processing symbol: {symbol}")

            # Load data
            pv_data, dl_data = self.load_symbol_data(symbol)

            if pv_data is None or pv_data.empty:
                logger.warning(f"No price-volume data found for {symbol}")
                return False

            # Merge data
            merged_data = self.merge_data(pv_data, dl_data)

            if merged_data.empty:
                logger.warning(f"No merged data for {symbol}")
                return False

            # Generate missing value report before filtering
            missing_report = self.get_missing_value_report(merged_data)
            if missing_report['features_with_missing'] > 0:
                logger.info(
                    f"Missing value analysis for {symbol}: "
                    f"{missing_report['features_with_missing']}/{missing_report['total_features']} features have missing values "
                    f"({missing_report['overall_missing_ratio']:.1%} overall missing ratio)"
                )

            # Filter high-missing features before extraction
            filtered_data = self.filter_high_missing_features(merged_data)

            if filtered_data.empty:
                logger.warning(f"No data remaining after filtering high-missing features for {symbol}")
                return False

            # Extract price and feature data
            price_data = self.extract_price_features(filtered_data)
            feature_data = self.extract_feature_data(filtered_data)

            if price_data.empty and feature_data.empty:
                logger.warning(f"No processable data for {symbol}")
                return False

            # Handle NaN values if enabled
            if self.handle_nan:
                if not price_data.empty:
                    price_data = self.handle_nan_values(price_data)
                if not feature_data.empty:
                    feature_data = self.handle_nan_values(feature_data)

            # Save processed data
            self.save_processed_data(symbol, price_data, feature_data)

            return True

        except Exception as e:
            logger.error(f"Failed to process symbol {symbol}: {e}")
            return False

    def process_all_symbols(self) -> Dict[str, bool]:
        """
        Process all available symbols.

        Returns:
            Dictionary mapping symbol names to success status
        """
        available_symbols = self.get_available_symbols()

        # Filter symbols if specified
        if self.selected_symbols:
            symbols_to_process = [s for s in available_symbols if s in self.selected_symbols]
        else:
            symbols_to_process = available_symbols

        logger.info(f"Processing {len(symbols_to_process)} symbols: {symbols_to_process}")

        results = {}
        for symbol in symbols_to_process:
            results[symbol] = self.process_symbol(symbol)

        # Summary
        successful = sum(1 for success in results.values() if success)
        total = len(results)
        logger.info(f"Processing complete: {successful}/{total} symbols processed successfully")

        return results

    def get_processing_summary(self) -> Dict[str, Any]:
        """
        Get summary of available data and processing configuration.

        Returns:
            Summary dictionary
        """
        available_symbols = self.get_available_symbols()

        summary = {
            'config': self.config,
            'available_symbols': available_symbols,
            'total_symbols': len(available_symbols),
            'dl_model_paths': self.dl_model_paths,
            'price_volume_path': self.price_volume_path,
            'output_path': self.output_path,
            'output_format': self.output_format,
            'selected_features': self.selected_features,
            'selected_symbols': self.selected_symbols
        }

        # Sample feature information
        if available_symbols:
            sample_symbol = available_symbols[0]
            sample_features = self.get_available_features(sample_symbol)
            summary['sample_features'] = sample_features

        return summary
