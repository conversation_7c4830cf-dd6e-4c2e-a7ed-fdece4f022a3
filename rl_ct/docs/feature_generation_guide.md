# 特征生成程序使用指南

## 概述

特征生成程序用于将现有的数据特征转换成DiskDataLoader需要的数据格式。该程序可以处理：

1. **深度学习模型输出**：包含预测分数和动作的数据
2. **量价特征**：包含技术指标和价格数据的特征

## 数据源

### 深度学习模型输出
- `data_snap/pred_model_5min_gain_v5_432882_online`
- `data_snap/pred_res_online_feat_kline_1m_with_vola_to_20210301_36symbols_v3`
- `data_snap/pred_res_online_feat_kline_1m_with_vola_to_20210301_36symbols_v9`

### 量价特征
- `data_snap/portfolio_v1`

所有数据文件以品种名称命名，格式为parquet。

## 输出格式

程序将数据转换为DiskDataLoader期望的格式：
- `prices.{format}` - 价格相关数据
- `features.{format}` - 特征数据
- `metadata.json` - 元数据信息

## 使用方法

### 1. 基本使用

```bash
# 处理所有品种
python rl_ct/scripts/generate_features.py --all

# 处理指定品种
python rl_ct/scripts/generate_features.py --symbols BTC#USDT:USDT ETH#USDT:USDT

# 查看可用数据摘要
python rl_ct/scripts/generate_features.py --summary
```

### 2. 自定义配置

```bash
# 使用自定义输出路径和格式
python rl_ct/scripts/generate_features.py --all \
    --output-path data/my_processed \
    --format npy

# 使用自定义深度学习模型路径
python rl_ct/scripts/generate_features.py --all \
    --dl-paths data_snap/model1 data_snap/model2

# 指定特定特征
python rl_ct/scripts/generate_features.py --all \
    --features close open high low rsi_7 macd_12
```

### 3. 使用配置文件

```bash
# 使用配置文件
python rl_ct/scripts/generate_features.py --all \
    --config rl_ct/configs/feature_generation_config.json
```

### 4. 调试和测试

```bash
# 干运行模式（不实际处理数据）
python rl_ct/scripts/generate_features.py --all --dry-run

# 详细日志
python rl_ct/scripts/generate_features.py --all --verbose
```

## 配置选项

### 命令行参数

| 参数 | 描述 | 默认值 |
|------|------|--------|
| `--all` | 处理所有可用品种 | - |
| `--symbols` | 指定要处理的品种列表 | - |
| `--summary` | 显示数据摘要并退出 | - |
| `--dl-paths` | 深度学习模型输出路径 | 默认三个路径 |
| `--pv-path` | 量价数据路径 | `data_snap/portfolio_v1` |
| `--output-path` | 输出目录 | `data/processed` |
| `--format` | 输出格式 | `parquet` |
| `--features` | 指定要包含的特征 | 所有特征 |
| `--config` | 配置文件路径 | - |
| `--verbose` | 详细日志 | - |
| `--dry-run` | 干运行模式 | - |

### 配置文件格式

配置文件使用JSON格式，示例：

```json
{
  "dl_model_paths": [
    "data_snap/pred_model_5min_gain_v5_432882_online",
    "data_snap/pred_res_online_feat_kline_1m_with_vola_to_20210301_36symbols_v3"
  ],
  "price_volume_path": "data_snap/portfolio_v1",
  "output_path": "data/processed",
  "output_format": "parquet",
  "selected_features": ["close", "rsi_7", "macd_12"],
  "selected_symbols": ["BTC#USDT:USDT", "ETH#USDT:USDT"]
}
```

## 特征类型

### 价格特征 (prices.{format})
- 基本价格数据：open, close, high, low, vol, val, vwap
- 收益率：returns, log_returns

### 技术指标特征 (features.{format})
- **移动平均**：sma_*, ema_*, wma_*, dema_*, tema_*, trima_*, kama_*, t3_*, ma_*
- **动量指标**：rsi_*, mom_*, roc_*, rocp_*, rocr_*
- **波动率指标**：var_*, stddev_*, atr_*, natr_*
- **趋势指标**：macd_*, adx_*, dx_*, cci_*, trix_*
- **布林带**：bBands_*
- **随机指标**：stochf_*, stoch_*, stochRsi_*
- **深度学习模型输出**：{model_name}_score, {model_name}_action

## 编程接口

### 使用FeatureGenerator类

```python
from rl_ct.utils.feature_generator import FeatureGenerator

# 创建配置
config = {
    'dl_model_paths': ['data_snap/pred_model_5min_gain_v5_432882_online'],
    'price_volume_path': 'data_snap/portfolio_v1',
    'output_path': 'data/processed',
    'output_format': 'parquet',
    'selected_symbols': ['BTC#USDT:USDT']
}

# 创建生成器
generator = FeatureGenerator(config)

# 处理单个品种
success = generator.process_symbol('BTC#USDT:USDT')

# 处理所有品种
results = generator.process_all_symbols()

# 获取摘要信息
summary = generator.get_processing_summary()
```

### 主要方法

- `get_available_symbols()` - 获取可用品种列表
- `get_available_features(symbol)` - 获取指定品种的可用特征
- `process_symbol(symbol)` - 处理单个品种
- `process_all_symbols()` - 处理所有品种
- `get_processing_summary()` - 获取处理摘要

## 输出结构

```
data/processed/
├── BTC#USDT:USDT/
│   ├── prices.parquet      # 价格数据
│   ├── features.parquet    # 特征数据
│   └── metadata.json       # 元数据
├── ETH#USDT:USDT/
│   ├── prices.parquet
│   ├── features.parquet
│   └── metadata.json
└── ...
```

## NaN值处理

程序提供智能的NaN值处理功能，根据不同类型的特征使用不同的填充策略：

### 填充策略

| 特征类型 | 填充策略 | 说明 |
|----------|----------|------|
| **价格特征** | 前向填充 | open, close, high, low, vwap等 |
| **成交量特征** | 零填充 | vol, val, ad, obv等 |
| **技术指标** | 前向填充 | RSI, MACD, 移动平均等 |
| **收益率/比率** | 零填充 | returns, log_returns, _cr等 |
| **波动率指标** | 前向填充 | var_, stddev_等 |
| **深度学习输出** | 前向填充 | _score, _action等 |

### 配置NaN处理

```bash
# 启用NaN处理（默认）
python rl_ct/scripts/generate_features.py --all --handle-nan

# 禁用NaN处理
python rl_ct/scripts/generate_features.py --all --no-handle-nan
```

### 自定义NaN策略

在配置文件中可以为特定列指定自定义策略：

```json
{
  "handle_nan": true,
  "nan_strategy_override": {
    "vol": "zero_fill",
    "custom_feature": "interpolate"
  }
}
```

支持的策略：
- `forward_fill`: 前向填充
- `zero_fill`: 零填充
- `backward_fill`: 后向填充
- `interpolate`: 线性插值

## 缺失值过滤

程序可以自动过滤缺失值超过阈值的特征，避免低质量特征影响模型性能：

### 过滤配置

```bash
# 设置缺失值阈值（默认50%）
python rl_ct/scripts/generate_features.py --all --missing-threshold 0.3

# 使用绝对数量阈值
python rl_ct/scripts/generate_features.py --all --missing-threshold 1000 --missing-threshold-mode count

# 禁用缺失值过滤
python rl_ct/scripts/generate_features.py --all --no-filter-missing
```

### 阈值模式

| 模式 | 说明 | 示例 |
|------|------|------|
| `ratio` | 按比例过滤 | `0.3` = 30%缺失值以上的特征被过滤 |
| `count` | 按绝对数量过滤 | `1000` = 超过1000个缺失值的特征被过滤 |

### 过滤效果

- **自动识别**：程序会自动分析每个特征的缺失情况
- **警告日志**：被过滤的特征会在日志中显示警告信息
- **统计报告**：提供详细的缺失值统计报告

## 注意事项

1. **数据对齐**：程序会自动按时间戳对齐不同数据源的数据
2. **NaN处理**：智能处理NaN值，根据特征类型使用不同策略
3. **内存使用**：大品种的数据可能占用较多内存，建议分批处理
4. **文件格式**：支持npy、csv、parquet三种输出格式
5. **元数据**：每个品种都会生成包含列信息和处理配置的元数据文件

## 故障排除

### 常见问题

1. **找不到数据文件**
   - 检查数据路径是否正确
   - 确认文件权限

2. **内存不足**
   - 减少同时处理的品种数量
   - 使用更高效的输出格式（如npy）

3. **数据格式错误**
   - 检查输入数据的parquet格式是否正确
   - 确认时间列格式

### 日志信息

使用 `--verbose` 参数可以获得详细的处理日志，包括：
- 数据加载状态
- 合并操作结果
- 特征提取信息
- 保存操作状态
