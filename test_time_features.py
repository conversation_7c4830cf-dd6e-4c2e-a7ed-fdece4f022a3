#!/usr/bin/env python3
"""
Test script to verify the time feature modifications work correctly.
"""

import numpy as np
import pandas as pd
from rl_ct.utils.preprocessing.feature_engineer import FeatureEngineer
from rl_ct.envs.crypto_trading_env import CryptoTradingEnv

def test_feature_engineer():
    """Test the modified FeatureEngineer with minute precision."""
    print("Testing FeatureEngineer with minute precision...")

    # Create sample data with datetime index (minute precision)
    dates = pd.date_range('2023-01-01', periods=100, freq='15min')  # 15-minute intervals
    sample_data = pd.DataFrame({
        'open': np.random.randn(100) + 100,
        'high': np.random.randn(100) + 102,
        'low': np.random.randn(100) + 98,
        'close': np.random.randn(100) + 100,
        'volume': np.random.randn(100) * 1000 + 10000
    }, index=dates)

    print(f"Sample data shape: {sample_data.shape}")
    print(f"Date range: {sample_data.index[0]} to {sample_data.index[-1]}")

    # Create feature engineer
    feature_engineer = FeatureEngineer()

    # Create technical indicators
    df_with_tech = feature_engineer.create_technical_indicators(sample_data)
    print(f"After technical indicators: {df_with_tech.shape}")

    # Create time features
    df_with_time = feature_engineer.create_time_features(df_with_tech)
    print(f"After time features: {df_with_time.shape}")

    # Check time features
    time_columns = []
    all_columns = list(df_with_time.columns)

    for col in all_columns:
        if any(time_word in col.lower() for time_word in ['minute', 'hour', 'day', 'week', 'session', 'weekend']):
            time_columns.append(col)

    print(f"\nTime-related columns ({len(time_columns)}):")
    for i, col in enumerate(time_columns):
        print(f"  {i}: {col}")

    # Show sample values for time features
    print(f"\nSample time feature values (first 5 rows):")
    time_features_df = df_with_time[time_columns].head()
    print(time_features_df)

    # Check that minute feature is present
    minute_present = 'minute' in all_columns
    print(f"\nMinute feature present: {minute_present}")

    # Check that cyclical encoding features are NOT present
    cyclical_features = [col for col in all_columns if any(x in col for x in ['_sin', '_cos'])]
    print(f"Cyclical encoding features (should be empty): {cyclical_features}")

    # Check that removed features are NOT present
    removed_features = [col for col in all_columns if any(x in col for x in ['day_of_month', 'month', 'quarter'])]
    print(f"Removed features (should be empty): {removed_features}")

    # Show the last few columns (where time features should be)
    print(f"\nLast 10 columns (time features should be here):")
    for i, col in enumerate(all_columns[-10:], len(all_columns)-10):
        print(f"  {i}: {col}")

    return df_with_time

def test_environment_integration():
    """Test that the environment works with the new time features."""
    print("\n" + "="*50)
    print("Testing Environment Integration...")
    
    # Create a minimal config for testing
    config = {
        'dataset_name': 'sample_dataset',
        'data_dir': 'data/processed',
        'lookback_window': 10,
        'initial_balance': 10000,
        'regime': 'training'
    }
    
    try:
        # Create environment
        env = CryptoTradingEnv(config)
        print(f"Environment created successfully")
        print(f"Time feature names: {env.time_feature_names}")
        print(f"Time feature indices: {env.time_feature_indices}")
        print(f"Feature data shape: {env.feature_data.shape}")
        print(f"Observation space: {env.observation_space}")

        # Check scaler configuration
        print(f"Scaler configuration:")
        print(f"  d_time: {env.scaler.d_time}")
        print(f"  d_account: {env.scaler.d_account}")
        print(f"  d_technical: {env.scaler.d_technical}")
        print(f"  time_scaler_type: {env.scaler.time_scaler}")
        print(f"  account_scaler_type: {type(env.scaler.account_scaler).__name__}")
        print(f"  technical_scaler_type: {type(env.scaler.technical_scaler).__name__}")

        # Test reset
        obs, info = env.reset()
        print(f"Reset successful, observation shape: {obs.shape}")

        # Test a few steps
        for i in range(3):
            action = env.action_space.sample()
            obs, reward, done, truncated, info = env.step(action)
            print(f"Step {i+1}: action={action}, reward={reward:.4f}, done={done}")

            if done or truncated:
                break
        
        print("Environment integration test passed!")
        return True
        
    except Exception as e:
        print(f"Environment integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("Testing Time Feature Modifications")
    print("="*50)
    
    # Test feature engineer
    df_with_features = test_feature_engineer()
    
    # Test environment integration
    success = test_environment_integration()
    
    print("\n" + "="*50)
    if success:
        print("All tests passed! Time feature modifications are working correctly.")
    else:
        print("Some tests failed. Please check the error messages above.")
    
    return success

if __name__ == "__main__":
    main()
