# 特征生成程序 (Feature Generator)

## 概述

这是一个用于将现有数据特征转换成DiskDataLoader需要格式的特征生成程序。该程序可以整合深度学习模型输出和量价特征数据，生成统一的训练数据格式。

## 功能特点

✅ **多数据源整合**：支持整合深度学习模型输出和量价特征数据
✅ **智能NaN处理**：根据特征类型自动选择最佳填充策略
✅ **缺失值过滤**：自动过滤缺失值超过阈值的低质量特征
✅ **灵活配置**：可配置数据路径、输出格式、特征选择等
✅ **批量处理**：支持处理所有品种或指定品种
✅ **多种输出格式**：支持parquet、npy、csv格式
✅ **数据验证**：自动验证生成数据的完整性
✅ **元数据记录**：自动生成包含特征信息的元数据文件
✅ **命令行工具**：提供易用的命令行接口

## 快速开始

### 1. 查看可用数据

```bash
python rl_ct/scripts/generate_features.py --summary
```

### 2. 处理单个品种

```bash
python rl_ct/scripts/generate_features.py --symbols BTC#USDT:USDT
```

### 3. 处理所有品种

```bash
python rl_ct/scripts/generate_features.py --all
```

### 4. 自定义配置

```bash
# 自定义输出路径和格式
python rl_ct/scripts/generate_features.py --all \
    --output-path data/my_processed \
    --format parquet \
    --features close open high low rsi_7 macd_12

# 禁用NaN处理
python rl_ct/scripts/generate_features.py --all --no-handle-nan

# 使用配置文件
python rl_ct/scripts/generate_features.py --all \
    --config rl_ct/configs/feature_generation_config.json
```

## 数据源

### 深度学习模型输出
- `data_snap/pred_model_5min_gain_v5_432882_online`
- `data_snap/pred_res_online_feat_kline_1m_with_vola_to_20210301_36symbols_v3`
- `data_snap/pred_res_online_feat_kline_1m_with_vola_to_20210301_36symbols_v9`

### 量价特征
- `data_snap/portfolio_v1`

## 输出格式

生成的数据符合DiskDataLoader的要求：

```
data/processed/
├── {SYMBOL}/
│   ├── prices.parquet      # 价格数据 (9列)
│   ├── features.parquet    # 特征数据 (161列)
│   └── metadata.json       # 元数据信息
```

### 价格数据包含
- 基本价格：open, close, high, low, vol, val, vwap
- 收益率：returns, log_returns

### 特征数据包含
- **技术指标**：移动平均、RSI、MACD、布林带等 (150+个)
- **深度学习模型输出**：预测分数、动作等 (每个模型5个特征)

## NaN值处理

程序提供智能的NaN值处理功能，根据不同类型的特征自动选择最佳填充策略：

### 填充策略

| 特征类型 | 填充策略 | 示例特征 |
|----------|----------|----------|
| 价格特征 | 前向填充 | open, close, high, low, vwap |
| 成交量特征 | 零填充 | vol, val, ad, obv |
| 技术指标 | 前向填充 | RSI, MACD, 移动平均 |
| 收益率/比率 | 零填充 | returns, log_returns, _cr |
| 深度学习输出 | 前向填充 | _score, _action |

### 处理效果

- **BTC数据示例**：成功处理334万个NaN值 → 0个NaN值
- **保持数据完整性**：不丢失任何行数据
- **智能策略选择**：根据特征语义选择合适的填充方法

## 缺失值过滤

程序可以自动识别并过滤缺失值过多的低质量特征：

### 过滤策略

```bash
# 设置30%缺失值阈值
python rl_ct/scripts/generate_features.py --all --missing-threshold 0.3

# 使用绝对数量阈值
python rl_ct/scripts/generate_features.py --all \
    --missing-threshold 1000 --missing-threshold-mode count

# 禁用过滤
python rl_ct/scripts/generate_features.py --all --no-filter-missing
```

### 过滤效果

- **质量控制**：自动移除低质量特征
- **警告日志**：详细记录被过滤的特征及原因
- **统计报告**：提供缺失值分析报告

## 编程接口

```python
from rl_ct.utils.feature_generator import FeatureGenerator

# 基本使用
generator = FeatureGenerator()
success = generator.process_symbol('BTC#USDT:USDT')

# 自定义配置
config = {
    'output_path': 'data/my_processed',
    'output_format': 'parquet',
    'selected_symbols': ['BTC#USDT:USDT', 'ETH#USDT:USDT'],
    'selected_features': ['close', 'rsi_7', 'macd_12']
}
generator = FeatureGenerator(config)
results = generator.process_all_symbols()
```

## 验证生成的数据

```python
from rl_ct.utils.data_loaders.disk_loader import DiskDataLoader

# 加载生成的数据
loader = DiskDataLoader(
    data_dir='data/processed',
    dataset_name='BTC#USDT:USDT',
    file_format='parquet'
)

price_data, feature_data = loader.load_data()
print(f'Price shape: {price_data.shape}')      # (2060640, 9)
print(f'Feature shape: {feature_data.shape}')  # (2060640, 161)
```

## 文件结构

```
rl_ct/
├── utils/
│   └── feature_generator.py          # 核心特征生成器
├── scripts/
│   └── generate_features.py          # 命令行工具
├── configs/
│   └── feature_generation_config.json # 配置文件示例
├── examples/
│   └── feature_generation_example.py  # 使用示例
├── tests/
│   └── test_feature_generator.py      # 测试脚本
└── docs/
    └── feature_generation_guide.md    # 详细使用指南
```

## 配置选项

| 配置项 | 描述 | 默认值 |
|--------|------|--------|
| `dl_model_paths` | 深度学习模型路径列表 | 3个默认路径 |
| `price_volume_path` | 量价数据路径 | `data_snap/portfolio_v1` |
| `output_path` | 输出目录 | `data/processed` |
| `output_format` | 输出格式 | `parquet` |
| `selected_features` | 选择的特征 | `null` (所有特征) |
| `selected_symbols` | 选择的品种 | `null` (所有品种) |
| `handle_nan` | 启用NaN处理 | `true` |
| `nan_strategy_override` | 自定义NaN策略 | `{}` (空字典) |
| `filter_high_missing` | 启用缺失值过滤 | `true` |
| `missing_threshold` | 缺失值过滤阈值 | `0.5` (50%) |
| `missing_threshold_mode` | 阈值模式 | `ratio` |

## 测试

```bash
# 运行测试
python rl_ct/tests/test_feature_generator.py

# 运行示例
python rl_ct/examples/feature_generation_example.py
```

## 性能统计

基于BTC#USDT:USDT的处理结果：

- **输入数据**：2,060,640行 × 176列 (合并后特征)
- **缺失值过滤**：1个特征被过滤 (100%缺失)
- **输出数据**：
  - 价格数据：2,060,640行 × 9列
  - 特征数据：2,060,640行 × 161列
- **NaN处理**：1,284,508个NaN值 → 0个NaN值
- **处理时间**：约27秒 (包含过滤和NaN处理)
- **文件大小**：
  - prices.parquet: ~17MB
  - features.parquet: ~334MB
  - metadata.json: ~4KB

## 注意事项

1. **内存使用**：大品种数据可能占用较多内存
2. **数据质量控制**：自动过滤低质量特征和处理NaN值
3. **时间对齐**：不同数据源会按时间戳自动对齐
4. **特征命名**：深度学习模型特征会添加模型名前缀
5. **数据完整性**：处理后的数据不包含NaN值，可直接用于模型训练
6. **过滤日志**：被过滤的特征会在日志中详细记录

## 故障排除

### 常见问题

1. **找不到数据文件**
   ```bash
   # 检查数据路径
   ls -la data_snap/portfolio_v1/
   ```

2. **内存不足**
   ```bash
   # 分批处理
   python rl_ct/scripts/generate_features.py --symbols BTC#USDT:USDT ETH#USDT:USDT
   ```

3. **权限问题**
   ```bash
   # 检查输出目录权限
   mkdir -p data/processed
   chmod 755 data/processed
   ```

## 更新日志

- **v1.2.0** (2025-08-05)
  - 新增缺失值过滤功能
  - 自动识别并过滤低质量特征
  - 支持比例和绝对数量两种阈值模式
  - 提供详细的缺失值分析报告

- **v1.1.0** (2025-08-05)
  - 新增智能NaN值处理功能
  - 根据特征类型自动选择填充策略
  - 支持自定义NaN处理策略
  - 完全消除输出数据中的NaN值

- **v1.0.0** (2025-08-05)
  - 初始版本发布
  - 支持多数据源整合
  - 提供命令行工具和编程接口
  - 完整的测试和文档

## 贡献

欢迎提交Issue和Pull Request来改进这个工具！
