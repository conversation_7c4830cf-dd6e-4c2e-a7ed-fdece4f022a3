{"cells": [{"cell_type": "code", "execution_count": 1, "id": "a25cbf91", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 2, "id": "5de9db27", "metadata": {}, "outputs": [], "source": ["features = pd.read_parquet('data/processed/BTC#USDT:USDT/features.parquet')\n", "prices = pd.read_parquet('data/processed/BTC#USDT:USDT/prices.parquet')"]}, {"cell_type": "code", "execution_count": 4, "id": "275e92a4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>close</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>vol</th>\n", "      <th>val</th>\n", "      <th>vwap</th>\n", "      <th>returns</th>\n", "      <th>log_returns</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>10772.64</td>\n", "      <td>10773.60</td>\n", "      <td>10781.00</td>\n", "      <td>10772.64</td>\n", "      <td>180.236</td>\n", "      <td>1.942455e+06</td>\n", "      <td>-1.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>10773.59</td>\n", "      <td>10772.93</td>\n", "      <td>10773.61</td>\n", "      <td>10765.81</td>\n", "      <td>150.870</td>\n", "      <td>1.624810e+06</td>\n", "      <td>-1.0</td>\n", "      <td>-0.000062</td>\n", "      <td>-0.000062</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>10773.23</td>\n", "      <td>10770.95</td>\n", "      <td>10774.63</td>\n", "      <td>10770.88</td>\n", "      <td>52.268</td>\n", "      <td>5.630729e+05</td>\n", "      <td>-1.0</td>\n", "      <td>-0.000184</td>\n", "      <td>-0.000184</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>10770.94</td>\n", "      <td>10770.01</td>\n", "      <td>10770.95</td>\n", "      <td>10770.00</td>\n", "      <td>21.741</td>\n", "      <td>2.341557e+05</td>\n", "      <td>-1.0</td>\n", "      <td>-0.000087</td>\n", "      <td>-0.000087</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>10770.00</td>\n", "      <td>10765.40</td>\n", "      <td>10770.01</td>\n", "      <td>10765.00</td>\n", "      <td>131.124</td>\n", "      <td>1.411820e+06</td>\n", "      <td>-1.0</td>\n", "      <td>-0.000428</td>\n", "      <td>-0.000428</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2060635</th>\n", "      <td>58955.30</td>\n", "      <td>58973.00</td>\n", "      <td>58973.00</td>\n", "      <td>58955.30</td>\n", "      <td>24.109</td>\n", "      <td>1.421470e+06</td>\n", "      <td>-1.0</td>\n", "      <td>0.000300</td>\n", "      <td>0.000300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2060636</th>\n", "      <td>58973.00</td>\n", "      <td>58969.00</td>\n", "      <td>58973.00</td>\n", "      <td>58958.90</td>\n", "      <td>30.557</td>\n", "      <td>1.801781e+06</td>\n", "      <td>-1.0</td>\n", "      <td>-0.000068</td>\n", "      <td>-0.000068</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2060637</th>\n", "      <td>58969.00</td>\n", "      <td>58979.80</td>\n", "      <td>58979.80</td>\n", "      <td>58968.90</td>\n", "      <td>20.372</td>\n", "      <td>1.201464e+06</td>\n", "      <td>-1.0</td>\n", "      <td>0.000183</td>\n", "      <td>0.000183</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2060638</th>\n", "      <td>58979.80</td>\n", "      <td>58980.00</td>\n", "      <td>58980.00</td>\n", "      <td>58979.70</td>\n", "      <td>15.012</td>\n", "      <td>8.854065e+05</td>\n", "      <td>-1.0</td>\n", "      <td>0.000003</td>\n", "      <td>0.000003</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2060639</th>\n", "      <td>58980.00</td>\n", "      <td>58983.70</td>\n", "      <td>58986.40</td>\n", "      <td>58975.50</td>\n", "      <td>43.426</td>\n", "      <td>2.561289e+06</td>\n", "      <td>-1.0</td>\n", "      <td>0.000063</td>\n", "      <td>0.000063</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2060640 rows × 9 columns</p>\n", "</div>"], "text/plain": ["             open     close      high       low      vol           val  vwap  \\\n", "0        10772.64  10773.60  10781.00  10772.64  180.236  1.942455e+06  -1.0   \n", "1        10773.59  10772.93  10773.61  10765.81  150.870  1.624810e+06  -1.0   \n", "2        10773.23  10770.95  10774.63  10770.88   52.268  5.630729e+05  -1.0   \n", "3        10770.94  10770.01  10770.95  10770.00   21.741  2.341557e+05  -1.0   \n", "4        10770.00  10765.40  10770.01  10765.00  131.124  1.411820e+06  -1.0   \n", "...           ...       ...       ...       ...      ...           ...   ...   \n", "2060635  58955.30  58973.00  58973.00  58955.30   24.109  1.421470e+06  -1.0   \n", "2060636  58973.00  58969.00  58973.00  58958.90   30.557  1.801781e+06  -1.0   \n", "2060637  58969.00  58979.80  58979.80  58968.90   20.372  1.201464e+06  -1.0   \n", "2060638  58979.80  58980.00  58980.00  58979.70   15.012  8.854065e+05  -1.0   \n", "2060639  58980.00  58983.70  58986.40  58975.50   43.426  2.561289e+06  -1.0   \n", "\n", "          returns  log_returns  \n", "0        0.000000     0.000000  \n", "1       -0.000062    -0.000062  \n", "2       -0.000184    -0.000184  \n", "3       -0.000087    -0.000087  \n", "4       -0.000428    -0.000428  \n", "...           ...          ...  \n", "2060635  0.000300     0.000300  \n", "2060636 -0.000068    -0.000068  \n", "2060637  0.000183     0.000183  \n", "2060638  0.000003     0.000003  \n", "2060639  0.000063     0.000063  \n", "\n", "[2060640 rows x 9 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["prices"]}, {"cell_type": "code", "execution_count": null, "id": "5542cf31", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Gym", "language": "python", "name": "gym"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}